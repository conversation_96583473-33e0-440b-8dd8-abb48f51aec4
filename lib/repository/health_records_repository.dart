import 'package:drift/drift.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../providers/database_provider.dart';
import '../types/health_record.dart';
import '../types/health_types.dart';
import 'database.dart';

part 'health_records_repository.g.dart';

/// 健康记录仓库 - 用于分页查询所有健康记录
class HealthRecordsRepository {
  final HealthDiaryDatabase _database;

  HealthRecordsRepository(this._database);

  /// 分页获取所有健康记录（血压和血糖）
  Future<List<HealthRecordEntry>> getHealthRecordsPaginated({
    required int page,
    required int pageSize,
  }) async {
    // 血糖
    final sugarQuery = _database.selectOnly(_database.bloodSugars)
      ..addColumns([
        _database.bloodSugars.id,
        _database.bloodSugars.createdAt,
        CustomExpression<int>(HealthRecordTypeEnum.bloodSugar.index.toString())
      ]);

    // 血压
    final pressureQuery = _database.selectOnly(_database.bloodPressures)
      ..addColumns([
        _database.bloodPressures.id,
        _database.bloodPressures.createdAt,
        CustomExpression<int>(HealthRecordTypeEnum.bloodPressure.index.toString())
      ]);

    final query = pressureQuery
        ..unionAll(sugarQuery)
        ..orderBy([OrderingTerm.desc(CustomExpression('created_at'))])
        ..limit(pageSize, offset: (page - 1) * pageSize);

    final ret = await query.get();
    for (final row in ret) {
      debugPrint(row.rawData.read("c2"));
    }

    //
    // final sql = _database.customSelect(
    //   '''
    //     SELECT * FROM (
    //       $sugarQuery
    //       UNION ALL
    //       $pressureQuery
    //
    //
    // final ret = await sql.get();
    // for (final row in ret) {
    //   debugPrint(row.data.toString());
    // }

    return List.empty();


    // 并行获取血压和血糖记录
    // final results = await Future.wait([
    //   _getBloodPressureRecordsPaginated(offset, pageSize),
    //   _getBloodSugarRecordsPaginated(offset, pageSize),
    // ]);
    //
    // final allRecords = <HealthRecordEntry>[];
    // allRecords.addAll(results[0]);
    // allRecords.addAll(results[1]);
    //
    // // 按创建时间降序排序
    // allRecords.sort((a, b) => _parseTime(b.time).compareTo(_parseTime(a.time)));
    //
    // // 返回指定页面大小的记录
    // return allRecords.take(pageSize).toList();
  }

  /// 分页获取血压记录
  Future<List<HealthRecordEntry>> _getBloodPressureRecordsPaginated(
    int offset,
    int limit,
  ) async {
    final records = await (_database.select(_database.bloodPressures)
          ..orderBy([(t) => OrderingTerm.desc(t.createdAt)])
          ..limit(limit, offset: offset))
        .get();

    return records.map(_convertBloodPressureToHealthRecord).toList();
  }

  /// 分页获取血糖记录
  Future<List<HealthRecordEntry>> _getBloodSugarRecordsPaginated(
    int offset,
    int limit,
  ) async {
    final records = await (_database.select(_database.bloodSugars)
          ..orderBy([(t) => OrderingTerm.desc(t.createdAt)])
          ..limit(limit, offset: offset))
        .get();

    return records.map(_convertBloodSugarToHealthRecord).toList();
  }

  /// 将血压记录转换为健康记录条目
  HealthRecordEntry _convertBloodPressureToHealthRecord(BloodPressure record) {
    return HealthRecordEntry(
      time: _formatDateTime(record.createdAt),
      type: 'blood_pressure'.tr(),
      note: record.note ?? '',
      recordType: HealthRecordTypeEnum.bloodPressure,
      color: 0xFFFFEBF0,
      values: [
        HealthRecordValue(
          label: 'systolic'.tr(),
          value: record.systolic.toString(),
          unit: 'mmHg',
        ),
        HealthRecordValue(
          label: 'diastolic'.tr(),
          value: record.diastolic.toString(),
          unit: 'mmHg',
        ),
        if (record.pulse != null)
          HealthRecordValue(
            label: 'pulse'.tr(),
            value: record.pulse.toString(),
            unit: 'bpm',
          ),
      ],
    );
  }

  /// 将血糖记录转换为健康记录条目
  HealthRecordEntry _convertBloodSugarToHealthRecord(BloodSugar record) {
    return HealthRecordEntry(
      time: _formatDateTime(record.createdAt),
      type: 'blood_sugar'.tr(),
      note: record.note ?? '',
      recordType: HealthRecordTypeEnum.bloodSugar,
      color: 0xFFE4F4FF,
      values: [
        HealthRecordValue(
          label: 'blood_sugar'.tr(),
          value: record.value.toStringAsFixed(1),
          unit: 'mmol/L',
        ),
      ],
    );
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final recordDate = DateTime(dateTime.year, dateTime.month, dateTime.day);

    final timeFormat = DateFormat('HH:mm');
    final timeString = timeFormat.format(dateTime);

    if (recordDate == today) {
      return '${'today'.tr()} $timeString';
    } else if (recordDate == yesterday) {
      return '${'yesterday'.tr()} $timeString';
    } else {
      final dateFormat = DateFormat('MM-dd');
      return '${dateFormat.format(dateTime)} $timeString';
    }
  }

  /// 解析时间字符串为 DateTime 用于排序
  DateTime _parseTime(String timeString) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    try {
      if (timeString.contains('today'.tr()) || timeString.contains('今天')) {
        final timeMatch = RegExp(r'(\d{2}):(\d{2})').firstMatch(timeString);
        if (timeMatch != null) {
          final hour = int.parse(timeMatch.group(1)!);
          final minute = int.parse(timeMatch.group(2)!);
          return today.add(Duration(hours: hour, minutes: minute));
        }
      } else if (timeString.contains('yesterday'.tr()) || timeString.contains('昨天')) {
        final timeMatch = RegExp(r'(\d{2}):(\d{2})').firstMatch(timeString);
        if (timeMatch != null) {
          final hour = int.parse(timeMatch.group(1)!);
          final minute = int.parse(timeMatch.group(2)!);
          return today.subtract(const Duration(days: 1)).add(Duration(hours: hour, minutes: minute));
        }
      } else {
        // 尝试解析 MM-dd HH:mm 格式
        final dateTimeMatch = RegExp(r'(\d{2})-(\d{2}) (\d{2}):(\d{2})').firstMatch(timeString);
        if (dateTimeMatch != null) {
          final month = int.parse(dateTimeMatch.group(1)!);
          final day = int.parse(dateTimeMatch.group(2)!);
          final hour = int.parse(dateTimeMatch.group(3)!);
          final minute = int.parse(dateTimeMatch.group(4)!);
          return DateTime(now.year, month, day, hour, minute);
        }
      }
    } catch (e) {
      // 如果解析失败，返回当前时间
      return now;
    }

    return now;
  }
}

/// 健康记录仓库提供者
@riverpod
HealthRecordsRepository healthRecordsRepository(Ref ref) {
  final database = ref.watch(databaseProvider);
  return HealthRecordsRepository(database);
}



// 直接用 SQL 查
// _database.selectOnly(_database.bloodSugars)
// ..addColumns([
// _database.bloodSugars.id,
//
// ]);
//
// final sugarSql = '''
//       SELECT
//         ${_database.bloodSugars.id.name},
//         ${_database.bloodSugars.createdAt.name},
//         ${HealthRecordTypeEnum.bloodSugar.index} as source
//       FROM
//         ${_database.bloodSugars.actualTableName}
//     ''';
//
// final pressuresSql = '''
//       SELECT
//         ${_database.bloodPressures.id.name},
//         ${_database.bloodPressures.createdAt.name},
//         ${HealthRecordTypeEnum.bloodPressure.index} as source
//       FROM
//         ${_database.bloodPressures.actualTableName}
//     ''';
//
//
// final offset = (page - 1) * pageSize;
//
// final sql = _database.customSelect('''
//           $pressuresSql
//           UNION ALL
//           $sugarSql
//           ORDER BY created_at DESC LIMIT 100 OFFSET ?
//         ''',
// variables: [Variable.withInt(offset)],
// readsFrom: {_database.bloodSugars, _database.bloodPressures}
// );
